"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import React, { useState, useEffect, useMemo } from "react";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Separator } from "@/components/ui/separator";
import { Check, Search, AlertTriangle, ShoppingCart, BellRing, Info, BellPlus, Loader2, DollarSign, TrendingUp, ServerCrash } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import type { PriceAlert } from "@/lib/types";

interface CryptoInfo {
  symbol: string;
  name: string;
}

interface FuturesTradingTerminalProps {
  availableCryptos: CryptoInfo[];
  activeAlerts: PriceAlert[];
  currentPrices: Record<string, string>;
  onScrollToAlerts?: () => void;
  apiKey: string | null;
  apiSecret: string | null;
}

const preprocessedNumberOptional = z.preprocess(
  (val) => {
    if (val === "" || val === null || val === undefined) {
      return undefined;
    }
    let stringVal = String(val);
    stringVal = stringVal.replace(/,/g, '.'); 
    stringVal = stringVal.replace(/[^\d.]/g, ""); 

    const firstDotIndex = stringVal.indexOf('.');
    if (firstDotIndex !== -1) {
      const integerPart = stringVal.substring(0, firstDotIndex);
      let fractionalPart = stringVal.substring(firstDotIndex + 1);
      fractionalPart = fractionalPart.replace(/\./g, ""); 
      stringVal = `${integerPart}.${fractionalPart}`;
    }
    if (stringVal === "" || stringVal === ".") {
        return undefined;
    }
    return stringVal;
  },
  z.coerce
    .number({ invalid_type_error: "Deve ser um número." })
    .positive("Deve ser positivo.")
    .optional()
);

const preprocessedPrice = z.preprocess(
  (val) => {
    if (val === "" || val === null || val === undefined) {
      return undefined;
    }
    let stringVal = String(val);
    stringVal = stringVal.replace(/,/g, '.');
    stringVal = stringVal.replace(/[^\d.]/g, ""); 
    const firstDotIndex = stringVal.indexOf('.');
    if (firstDotIndex !== -1) {
      const integerPart = stringVal.substring(0, firstDotIndex);
      let fractionalPart = stringVal.substring(firstDotIndex + 1);
      fractionalPart = fractionalPart.replace(/\./g, "");
      stringVal = `${integerPart}.${fractionalPart}`;
    }
    if (stringVal === "" || stringVal === ".") {
        return undefined;
    }
    return stringVal;
  },
  z.coerce
    .number({ invalid_type_error: "Preço deve ser um número." })
    .positive("Preço deve ser positivo.")
    .optional()
);

const orderFormSchemaBase = z.object({
  symbol: z.string().min(1, "Por favor, selecione uma criptomoeda."),
  orderType: z.enum(["market", "limit"]),
  side: z.enum(["buy", "sell"]),
  price: preprocessedPrice,
  orderUsdtAmount: z.preprocess( 
    (val) => {
      if (val === "" || val === null || val === undefined) return undefined;
      let stringVal = String(val).replace(/,/g, '.').replace(/[^\d.]/g, "");
      const firstDotIndex = stringVal.indexOf('.');
      if (firstDotIndex !== -1) {
        const integerPart = stringVal.substring(0, firstDotIndex);
        const fractionalPart = stringVal.substring(firstDotIndex + 1).replace(/\./g, "");
        stringVal = `${integerPart}.${fractionalPart}`;
      }
      if (stringVal === "" || stringVal === ".") return undefined;
      return stringVal;
    },
    z.coerce.number({invalid_type_error: "Valor da ordem deve ser um número."}).positive("Valor da ordem deve ser positivo.")
  ),
  leverage: z.number().min(1).max(125),
  takeProfit: preprocessedNumberOptional,
  stopLoss: preprocessedNumberOptional,
});


const orderFormSchema = (currentMinNotionalValue: number | null) => orderFormSchemaBase.refine(
  (data) => {
    if (data.orderType === "limit") {
      return data.price !== undefined && data.price > 0;
    }
    return true;
  },
  {
    message: "O preço é obrigatório e deve ser positivo para ordens limite.",
    path: ["price"],
  }
).refine(
  (data) => {
    if (currentMinNotionalValue !== null && data.orderUsdtAmount !== undefined) {
        // More tolerant validation with better floating point handling
        const tolerance = 0.01; // 1 cent tolerance
        return data.orderUsdtAmount >= (currentMinNotionalValue - tolerance);
    }
    return true; 
  },
  {
    message: `Valor da ordem deve ser maior ou igual ao mínimo nocional.`,
    path: ["orderUsdtAmount"],
  }
);


type OrderFormValues = z.infer<typeof orderFormSchemaBase>; 

type ConfirmedOrderDetails = OrderFormValues & {
  
  quantity: number; 
  estimatedMargin: number;
  apiKey?: string;
  apiSecret?: string;
  stepSize?: string | null;
  minQty?: string | null;
  tickSize?: string | null;
};


export function FuturesTradingTerminal({
  availableCryptos,
  activeAlerts,
  currentPrices,
  onScrollToAlerts,
  apiKey,
  apiSecret,
}: FuturesTradingTerminalProps) {
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [isConfirmingOrder, setIsConfirmingOrder] = useState(false);
  const [confirmedOrderDetails, setConfirmedOrderDetails] = useState<ConfirmedOrderDetails | null>(null);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  const [currentMinNotional, setCurrentMinNotional] = useState<string | null>(null);
  const [numericMinNotional, setNumericMinNotional] = useState<number | null>(null);
  const [currentStepSize, setCurrentStepSize] = useState<string | null>(null);
  const [currentMinQty, setCurrentMinQty] = useState<string | null>(null);
  const [currentTickSize, setCurrentTickSize] = useState<string | null>(null);
  const [isLoadingSymbolInfo, setIsLoadingSymbolInfo] = useState(false);
  const [symbolInfoError, setSymbolInfoError] = useState<string | null>(null);

  const [displayMarketPrice, setDisplayMarketPrice] = useState<string | null>(null);
  const [estimatedMargin, setEstimatedMargin] = useState<number | null>(null);
  const [calculatedMinNotional, setCalculatedMinNotional] = useState<number | null>(null);
  const [isUserEditing, setIsUserEditing] = useState(false);
  const [lastAutoFilledValue, setLastAutoFilledValue] = useState<number | null>(null);


  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema(calculatedMinNotional ?? numericMinNotional)),
    defaultValues: {
      symbol: "BTCUSDT",
      orderType: "market",
      side: "buy",
      price: undefined,
      orderUsdtAmount: undefined, 
      leverage: 10,
      takeProfit: undefined,
      stopLoss: undefined,
    },
    mode: "onChange", 
  });

  const selectedSymbol = form.watch("symbol");
  const currentOrderType = form.watch("orderType");
  const currentLeverage = form.watch("leverage");
  const currentOrderUsdtAmount = form.watch("orderUsdtAmount");


  useEffect(() => {
    
    form.trigger();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [numericMinNotional, calculatedMinNotional, form.formState.isValid]);


  const hasActiveAlertForSymbol = useMemo(() => {
    if (!selectedSymbol) return false;
    const normalizedSelectedSymbol = selectedSymbol.replace(/USDT|BUSD$/, '');
    return activeAlerts.some(alert => (alert.symbol === normalizedSelectedSymbol || alert.cryptoSymbol === normalizedSelectedSymbol) && alert.status === 'active');
  }, [selectedSymbol, activeAlerts]);


  useEffect(() => {
    const abortController = new AbortController();

    const resetSymbolData = () => {
        setCurrentMinNotional(null);
        setNumericMinNotional(null);
        setCurrentStepSize(null);
        setCurrentMinQty(null);
        setCurrentTickSize(null);
        setEstimatedMargin(null);
        setDisplayMarketPrice(null);
        setSymbolInfoError(null);
        setIsLoadingSymbolInfo(false);
        setCalculatedMinNotional(null);
        setIsUserEditing(false);
        setLastAutoFilledValue(null);
        const currentFormValue = form.getValues("orderUsdtAmount");
        if (currentFormValue !== undefined) {
          form.setValue("orderUsdtAmount", currentFormValue, { shouldValidate: true });
        }
    };
    
    if (selectedSymbol) {
      setIsLoadingSymbolInfo(true);
      setSymbolInfoError(null); 
      setEstimatedMargin(null); 
      setDisplayMarketPrice(null); 


      fetch(`/api/binance/symbol-info?symbol=${selectedSymbol}`, { signal: abortController.signal })
        .then(async res => {
          if (abortController.signal.aborted) return Promise.reject(new DOMException('Aborted', 'AbortError'));
          if (!res.ok) {
            const errorBody = await res.json().catch(() => ({ message: `Falha ao buscar info para ${selectedSymbol}. Status: ${res.status}` }));
            throw new Error(errorBody.message || `Falha ao buscar info para ${selectedSymbol}.`);
          }
          return res.json();
        })
        .then(data => {
          if (abortController.signal.aborted) return;
          if (data.success && data.data) {
            const fetchedMinNotional = data.data.minNotional;
            const fetchedStepSize = data.data.lotSize?.stepSize;
            const fetchedMinQty = data.data.lotSize?.minQty;
            const fetchedTickSize = data.data.priceFilter?.tickSize;

            let errorMessages: string[] = []; // eslint-disable-line prefer-const
            let localNumericMinNotional: number | null = null;

            if (fetchedMinNotional && parseFloat(fetchedMinNotional) > 0) {
                setCurrentMinNotional(parseFloat(fetchedMinNotional).toFixed(2));
                localNumericMinNotional = parseFloat(fetchedMinNotional);
                setNumericMinNotional(localNumericMinNotional);
                
                if(form.getValues("orderUsdtAmount") === undefined || form.getValues("orderUsdtAmount")! < localNumericMinNotional ) {
                    const autoFillValue = Math.ceil(localNumericMinNotional * 100) / 100;
                    form.setValue("orderUsdtAmount", autoFillValue, { shouldValidate: true });
                    setLastAutoFilledValue(autoFillValue);
                }
            } else {
                errorMessages.push(`MinNotional inválido/ausente ('${fetchedMinNotional || 'N/A'}'). Usando fallback $5.00.`);
                setCurrentMinNotional("5.00"); 
                localNumericMinNotional = 5.00;
                setNumericMinNotional(localNumericMinNotional);
                if(form.getValues("orderUsdtAmount") === undefined || form.getValues("orderUsdtAmount")! < localNumericMinNotional ) {
                    const autoFillValue = Math.ceil(localNumericMinNotional * 100) / 100;
                    form.setValue("orderUsdtAmount", autoFillValue, { shouldValidate: true });
                    setLastAutoFilledValue(autoFillValue);
                }
            }

            if (!fetchedStepSize || parseFloat(fetchedStepSize) <= 0) {
                errorMessages.push(`StepSize inválido/ausente ('${fetchedStepSize || 'N/A'}').`);
                setCurrentStepSize(null);
            } else {
                setCurrentStepSize(fetchedStepSize);
            }

            if (fetchedMinQty === null || fetchedMinQty === undefined || parseFloat(fetchedMinQty) < 0) {
                 errorMessages.push(`MinQty inválido/ausente ('${fetchedMinQty || 'N/A'}').`);
                 setCurrentMinQty(null);
            } else {
                 setCurrentMinQty(fetchedMinQty);
            }
            
            if (!fetchedTickSize || parseFloat(fetchedTickSize) <= 0) {
                errorMessages.push(`TickSize inválido/ausente ('${fetchedTickSize || 'N/A'}') para precisão de preço.`);
                setCurrentTickSize(null);
            } else {
                setCurrentTickSize(fetchedTickSize);
            }

            if (errorMessages.length > 0) {
                const fullErrorMsg = `${errorMessages.join(' ')} Para ${selectedSymbol}. Negociação pode estar desabilitada ou falhar.`;
                console.error(fullErrorMsg);
                setSymbolInfoError(fullErrorMsg);
            } else {
                setSymbolInfoError(null); 
            }
          } else {
            const errorMsg = data.message || `Dados do símbolo incompletos para ${selectedSymbol}. Negociação desabilitada.`;
            console.warn(errorMsg);
            setSymbolInfoError(errorMsg);
            
            setCurrentMinNotional(null);
            setNumericMinNotional(null);
            setCurrentStepSize(null);
            setCurrentMinQty(null);
            setCurrentTickSize(null);
          }
        })
        .catch(err => {
          if (err.name === 'AbortError') {
            console.log('Fetch para info do símbolo abortado para', selectedSymbol);
            return;
          }
          if (abortController.signal.aborted && err.name !== 'AbortError') return;

          console.error(`Erro ao buscar informações do símbolo para ${selectedSymbol}:`, err);
          const errorMessage = err instanceof Error ? err.message : "Não foi possível carregar informações do símbolo.";
          setSymbolInfoError(errorMessage + " Negociação pode não estar disponível para este par.");
          
          setCurrentMinNotional(null);
          setNumericMinNotional(null);
          setCurrentStepSize(null);
          setCurrentMinQty(null);
          setCurrentTickSize(null);
        })
        .finally(() => {
           if (abortController.signal.aborted) return;
          setIsLoadingSymbolInfo(false);
        });
    } else {
        
        setCurrentMinNotional(null);
        setNumericMinNotional(null);
        setCurrentStepSize(null);
        setCurrentMinQty(null);
        setCurrentTickSize(null);
        setEstimatedMargin(null);
        setDisplayMarketPrice(null);
        setSymbolInfoError(null); 
        setIsLoadingSymbolInfo(false); 
    }
     return () => {
      abortController.abort();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSymbol]); 


  useEffect(() => {
    if (currentOrderUsdtAmount && !isLoadingSymbolInfo && currentLeverage > 0 && currentOrderUsdtAmount > 0) { 
        setEstimatedMargin(currentOrderUsdtAmount / currentLeverage);
    } else if (currentOrderUsdtAmount === 0 || currentOrderUsdtAmount === undefined) {
        setEstimatedMargin(null);
    } else if (!currentMinNotional && !isLoadingSymbolInfo && selectedSymbol && !symbolInfoError) { 
        setEstimatedMargin(null);
    }
  }, [currentOrderUsdtAmount, currentLeverage, isLoadingSymbolInfo, symbolInfoError, currentMinNotional, selectedSymbol]);


  useEffect(() => {
    if (selectedSymbol && currentPrices) {
      const pricePair = selectedSymbol.toUpperCase();
      const price = currentPrices[pricePair];
      if (price) {
        try {
          const priceNum = parseFloat(price);
          let tickSizeDecimalPlaces = 2; 
          if (currentTickSize && parseFloat(currentTickSize) > 0) {
            const ts = parseFloat(currentTickSize);
            if (ts < 1) { 
                 tickSizeDecimalPlaces = currentTickSize.split('.')[1]?.length || 0;
                 if (currentTickSize.toLowerCase().includes('e-')) {
                    const parts = currentTickSize.toLowerCase().split('e-');
                    if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
                        tickSizeDecimalPlaces = parseInt(parts[1]);
                    }
                 }
            } else { 
                tickSizeDecimalPlaces = 0; 
            }
          } else { 
              if (priceNum < 0.0001) tickSizeDecimalPlaces = 8;
              else if (priceNum < 0.01) tickSizeDecimalPlaces = 6;
              else if (priceNum < 1) tickSizeDecimalPlaces = 4;
          }
          tickSizeDecimalPlaces = Math.min(8, Math.max(priceNum >= 1 ? 2 : tickSizeDecimalPlaces, 0));
          setDisplayMarketPrice(priceNum.toLocaleString('pt-BR', { style: 'currency', currency: 'USD', minimumFractionDigits: tickSizeDecimalPlaces, maximumFractionDigits: 8 }));
        } catch (e) {
          console.error("Erro ao formatar o preço de mercado:", e);
          setDisplayMarketPrice(price); 
        }
      } else {
        setDisplayMarketPrice(null);
      }
    } else {
      setDisplayMarketPrice(null);
    }
  }, [selectedSymbol, currentPrices, currentTickSize]);


  const onSubmit = (data: OrderFormValues) => {
    const effectiveMinNotional = calculatedMinNotional ?? numericMinNotional;
    
    if (isLoadingSymbolInfo || 
        !currentStepSize || parseFloat(currentStepSize) <= 0 || 
        currentMinQty === null || parseFloat(currentMinQty) < 0 ||
        (data.orderType === 'limit' && (!currentTickSize || parseFloat(currentTickSize) <= 0)) ||
        data.orderUsdtAmount === undefined || data.orderUsdtAmount <= 0 ||
        (effectiveMinNotional !== null && data.orderUsdtAmount < effectiveMinNotional)
        ) {
      let errorDetail = symbolInfoError || "";
      if (!currentStepSize || parseFloat(currentStepSize) <= 0) errorDetail += " StepSize inválido/ausente.";
      if (currentMinQty === null || parseFloat(currentMinQty) < 0) errorDetail += " MinQty inválido/ausente.";
      if (data.orderType === 'limit' && (!currentTickSize || parseFloat(currentTickSize) <= 0)) errorDetail += " TickSize inválido/ausente para ordem limite.";
      if (data.orderUsdtAmount === undefined || data.orderUsdtAmount <= 0) errorDetail += " Valor nocional da ordem inválido.";
      else if (effectiveMinNotional !== null && data.orderUsdtAmount < effectiveMinNotional) errorDetail += ` Valor da ordem deve ser ao menos ${effectiveMinNotional.toFixed(2)} USDT.`;

      toast({
        title: "Dados Inválidos do Símbolo ou Ordem",
        description: errorDetail || `Dados críticos estão ausentes ou inválidos para ${selectedSymbol}. Tente selecionar outro símbolo ou ajuste os valores da ordem.`,
        variant: "destructive",
        duration: 7000,
      });
      return;
    }

    if (data.orderUsdtAmount === undefined) { 
       toast({ title: "Erro de Valor Nocional", description: `Valor nocional para a ordem não definido. Não é possível colocar a ordem.`, variant: "destructive" });
       return;
    }
    
    const notionalValueToUse = data.orderUsdtAmount;
    const calculatedMargin = notionalValueToUse / data.leverage;

    if (isNaN(calculatedMargin) || calculatedMargin <=0){
        toast({ title: "Erro de Cálculo de Margem", description: `Não foi possível calcular a margem estimada com os valores atuais. Verifique o valor nocional e a alavancagem.`, variant: "destructive" });
        return;
    }

    const orderDetailsWithFullInfo: ConfirmedOrderDetails = {
      ...data,
      quantity: notionalValueToUse, 
      estimatedMargin: calculatedMargin,
      apiKey: apiKey ?? undefined,
      apiSecret: apiSecret ?? undefined,
      stepSize: currentStepSize,
      minQty: currentMinQty,
      tickSize: currentTickSize,
    };
    setConfirmedOrderDetails(orderDetailsWithFullInfo);
    setIsConfirmingOrder(true);
  };

  const handleConfirmOrder = async () => {
    if (!confirmedOrderDetails) {
      toast({ title: "Erro Interno", description: "Detalhes da ordem ausentes.", variant: "destructive" });
      setIsConfirmingOrder(false);
      return;
    }

    if (!apiKey || !apiSecret) {
      toast({ title: "Chaves de API Ausentes", description: "As chaves de API do cliente não estão configuradas nas Configurações. Não é possível colocar a ordem.", variant: "destructive" });
      setIsConfirmingOrder(false);
      setConfirmedOrderDetails(null);
      return;
    }
     if (!confirmedOrderDetails.stepSize || parseFloat(confirmedOrderDetails.stepSize) <= 0 || 
         confirmedOrderDetails.minQty === null || confirmedOrderDetails.minQty === undefined || parseFloat(confirmedOrderDetails.minQty) < 0 ||
         (confirmedOrderDetails.orderType === 'limit' && (!confirmedOrderDetails.tickSize || parseFloat(confirmedOrderDetails.tickSize) <= 0))
         ) {
        let errorDesc = `StepSize ('${confirmedOrderDetails.stepSize}') ou minQty ('${confirmedOrderDetails.minQty}') inválidos.`;
        if (confirmedOrderDetails.orderType === 'limit' && (!confirmedOrderDetails.tickSize || parseFloat(confirmedOrderDetails.tickSize) <= 0)) {
            errorDesc += ` TickSize ('${confirmedOrderDetails.tickSize}') inválido para ordem limite.`
        }
        toast({ title: "Erro de Configuração do Símbolo", description: errorDesc + " Não é possível colocar a ordem.", variant: "destructive" });
        setIsConfirmingOrder(false);
        setConfirmedOrderDetails(null);
        return;
    }

    setIsPlacingOrder(true);
    try {
      const response = await fetch('/api/binance/futures/place-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            ...confirmedOrderDetails,
            apiKey,
            apiSecret,
        }),
      });
      const result = await response.json();
      
      // Check for order ID in different possible response structures
      let finalOrderId = 'N/A';
      let isSuccess = false;
      
      // Try to extract order ID from different response structures
      if (result.orderResult && result.orderResult.orderId && typeof result.orderResult.orderId === 'number' && result.orderResult.orderId > 0) {
        finalOrderId = String(result.orderResult.orderId);
        isSuccess = true;
      } else if (result.orderId && typeof result.orderId === 'number' && result.orderId > 0) {
        finalOrderId = String(result.orderId);
        isSuccess = true;
      }
      
      // Check for success indicators
      const hasSuccessIndicator = result.success === true || result.status === 'NEW' || result.status === 'FILLED' || result.status === 'PARTIALLY_FILLED';
      const hasValidStatus = response.ok && !result.code && !result.error;
      
      if (hasValidStatus && (isSuccess || hasSuccessIndicator)) {
        const successMessage = result.message || `Ordem colocada com sucesso na Binance!`;
        toast({
          title: "Sucesso!",
          description: `${successMessage} (ID da Ordem Binance: ${finalOrderId})`,
          duration: 10000,
          className: "bg-green-100 dark:bg-green-800 border-green-500",
        });
      } else {
        const errorMessage = result.message || result.msg || `Falha ao colocar ordem. Status: ${response.status}`;
        throw new Error(errorMessage);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Ocorreu um erro desconhecido.";
      toast({ title: "Falha ao Colocar Ordem", description: errorMessage, variant: "destructive", duration: 10000 });
    } finally {
      setIsPlacingOrder(false);
      setIsConfirmingOrder(false);
      setConfirmedOrderDetails(null);
      
      const currentSymbolValue = form.getValues("symbol"); 
      form.reset({ 
        symbol: currentSymbolValue, 
        orderType: "market",
        side: "buy",
        price: undefined,
                 orderUsdtAmount: (calculatedMinNotional ?? numericMinNotional) ?? undefined, 
        leverage: 10,
        takeProfit: undefined,
        stopLoss: undefined,
      });
      
      setCurrentMinNotional(null);
      setNumericMinNotional(null);
      setCurrentStepSize(null);
      setCurrentMinQty(null);
      setCurrentTickSize(null);
            setEstimatedMargin(null);
      setDisplayMarketPrice(null);
      setSymbolInfoError(null);
      setIsLoadingSymbolInfo(false);
      setIsUserEditing(false);
      setLastAutoFilledValue(null);

       const finalEffectiveMinNotional = calculatedMinNotional ?? numericMinNotional;
        if (form.getValues("symbol") === currentSymbolValue && finalEffectiveMinNotional) {
          const autoFillValue = Math.ceil(finalEffectiveMinNotional * 100) / 100;
          form.setValue("orderUsdtAmount", autoFillValue, { shouldValidate: true });
          setLastAutoFilledValue(autoFillValue);
        }
    }
  };

  const handleCreateAlertForSymbol = () => {
    if (selectedSymbol && onScrollToAlerts) {
      onScrollToAlerts();
    }
  };

  const assetSymbolDisplay = form.watch('symbol')?.replace(/USDT|BUSD$/, '') || 'Ativo';
  const formSide = form.watch("side");

  const clientKeysPresent = !!apiKey && !!apiSecret;

  // Handler to detect manual editing by user
  const handleOrderAmountChange = (value: string) => {
    const numValue = parseFloat(value) || 0;
    const effectiveMinNotional = calculatedMinNotional ?? numericMinNotional;
    
    // Check if user is manually editing (changing away from auto-filled value)
    if (lastAutoFilledValue !== null && Math.abs(numValue - lastAutoFilledValue) > 0.01) {
      setIsUserEditing(true);
    }
    
    // If user clears the field or enters a value below minimum, reset to auto mode
    if (value === "" || (effectiveMinNotional && numValue < effectiveMinNotional - 0.01)) {
      setIsUserEditing(false);
      setLastAutoFilledValue(null);
    }
    
    return value;
  };

  // New useEffect to calculate real minimum notional based on current price and minQty
  useEffect(() => {
    if (selectedSymbol && currentPrices && currentMinQty && !isLoadingSymbolInfo) {
      const pricePair = selectedSymbol.toUpperCase();
      const price = currentPrices[pricePair];
      
      if (price && currentMinQty) {
        try {
          const priceNum = parseFloat(price);
          const minQtyNum = parseFloat(currentMinQty);
          
          if (!isNaN(priceNum) && !isNaN(minQtyNum) && priceNum > 0 && minQtyNum >= 0) {
            // Calculate minimum notional: minQty * currentPrice
            const calculatedMin = minQtyNum * priceNum;
            
            // Use the higher value between static minNotional and calculated minimum
            let finalMinNotional = calculatedMin;
            if (numericMinNotional && numericMinNotional > calculatedMin) {
              finalMinNotional = numericMinNotional;
            }
            
            setCalculatedMinNotional(finalMinNotional);
            
            // Auto-fill logic: only update if user is not manually editing
            const currentOrderAmount = form.getValues("orderUsdtAmount");
            
            // Check if we should auto-fill
            const shouldAutoFill = !isUserEditing && (
              currentOrderAmount === undefined || 
              currentOrderAmount < finalMinNotional ||
              (lastAutoFilledValue !== null && Math.abs(currentOrderAmount - lastAutoFilledValue) < 0.01)
            );
            
            if (shouldAutoFill) {
              // Use the exact minimum value to avoid validation errors
              const autoFillValue = Math.ceil(finalMinNotional * 100) / 100; // Round to 2 decimal places
              form.setValue("orderUsdtAmount", autoFillValue, { shouldValidate: true });
              setLastAutoFilledValue(autoFillValue);
              
              console.log(`Auto-filled order amount for ${selectedSymbol}: ${finalMinNotional.toFixed(2)} -> ${autoFillValue} USDT`);
            }
          }
        } catch (e) {
          console.error("Error calculating minimum notional:", e);
          setCalculatedMinNotional(null);
        }
      } else {
        setCalculatedMinNotional(null);
      }
    } else {
      setCalculatedMinNotional(null);
    }
  }, [selectedSymbol, currentPrices, currentMinQty, numericMinNotional, isLoadingSymbolInfo, isUserEditing, lastAutoFilledValue, form]);

  return (
    <Card className="card-interactive col-span-1 md:col-span-2">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-6 w-6 text-primary" />
          Terminal de Negociação de Futuros
        </CardTitle>
        <CardDescription>
          Coloque ordens para contratos futuros. As ordens são enviadas para a API da Binance usando as chaves configuradas pelo usuário nas Configurações.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-2 mb-4 border border-destructive/50 bg-destructive/10 rounded-md flex items-start gap-3">
          <AlertTriangle className="h-8 w-8 text-destructive flex-shrink-0 mt-1" />
          <div>
            <h4 className="font-semibold text-destructive">AVISO CRÍTICO: OPERAÇÕES REAIS</h4>
            <p className="text-xs text-destructive/80 break-words">
              Este terminal tentará executar **ORDENS REAIS** na Binance Futures utilizando as chaves de API fornecidas pelo usuário (configuradas nas Configurações).
              **Use por sua conta e risco extremo. É altamente recomendável testar APENAS no Testnet da Binance ou com valores muito pequenos e após entender completamente o código e os riscos envolvidos.**
            </p>
             {(!clientKeysPresent) && (
                <p className="text-xs text-destructive/90 font-semibold mt-1.5">
                    <ServerCrash className="inline h-4 w-4 mr-1"/> ALERTA: Chaves de API não configuradas nas Configurações. As ordens não poderão ser processadas.
                </p>
            )}
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-3 p-4 border rounded-lg bg-card/60 shadow-sm">
                <FormField
                    control={form.control}
                    name="symbol"
                    render={({ field }) => (
                    <FormItem className="flex flex-col">
                        <FormLabel>Símbolo do Contrato (Ex: BTCUSDT)</FormLabel>
                        <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                        <PopoverTrigger asChild>
                            <FormControl>
                            <Button
                                variant="outline"
                                role="combobox"
                                className={cn( "w-full justify-between", !field.value && "text-muted-foreground" )}
                            >
                                {field.value
                                ? availableCryptos.find( (crypto) => (crypto.symbol + 'USDT') === field.value || crypto.symbol === field.value )?.name
                                    ? `${availableCryptos.find(crypto => (crypto.symbol + 'USDT') === field.value || crypto.symbol === field.value)?.name} (${field.value})`
                                    : field.value
                                : "Selecione um contrato"}
                                <div className="flex items-center">
                                {hasActiveAlertForSymbol && <BellRing className="mr-2 h-4 w-4 text-primary animate-pulse" />}
                                <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </div>
                            </Button>
                            </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                            <Command>
                            <CommandInput placeholder="Buscar contrato..." />
                            <CommandList>
                                <CommandEmpty>Nenhuma criptomoeda encontrada.</CommandEmpty>
                                <CommandGroup>
                                {availableCryptos.map((crypto) => (
                                    <CommandItem
                                    value={crypto.symbol + 'USDT'}
                                    key={crypto.symbol}
                                    onSelect={() => {
                                        form.setValue("symbol", crypto.symbol + 'USDT', {shouldValidate: true, shouldDirty: true, shouldTouch: true });
                                        setPopoverOpen(false);
                                    }}
                                    >
                                    <Check className={cn( "mr-2 h-4 w-4", field.value === (crypto.symbol + 'USDT') ? "opacity-100" : "opacity-0" )}/>
                                    {crypto.name} ({crypto.symbol}USDT)
                                    </CommandItem>
                                ))}
                                </CommandGroup>
                            </CommandList>
                            </Command>
                        </PopoverContent>
                        </Popover>
                        <FormMessage />
                    </FormItem>
                    )}
                />
                {selectedSymbol && (
                    <div className="space-y-1 text-sm">
                        {isLoadingSymbolInfo && !symbolInfoError && !displayMarketPrice ? (
                            <div className="text-xs text-muted-foreground flex items-center"> <Loader2 className="mr-1.5 h-3.5 w-3.5 animate-spin text-primary/70" /> Carregando preço de referência... </div>
                        ) : !isLoadingSymbolInfo && displayMarketPrice && !symbolInfoError ? (
                            <div className="text-muted-foreground flex items-center">
                                <TrendingUp className="mr-1.5 h-4 w-4 text-primary/80" /> Preço de Referência: <span className="font-semibold ml-1 text-foreground">{displayMarketPrice}</span>
                            </div>
                        ) : !isLoadingSymbolInfo && !symbolInfoError && !displayMarketPrice && selectedSymbol ? (
                              <div className="text-xs text-amber-600 dark:text-amber-500 flex items-center"> <Info className="mr-1.5 h-3.5 w-3.5"/> Preço não disponível para {selectedSymbol}. </div>
                        ) : null }


                        {symbolInfoError && !isLoadingSymbolInfo && (
                             <div className="text-destructive flex items-center gap-1 text-xs mt-1 break-words"><ServerCrash className="h-3.5 w-3.5"/> {symbolInfoError}</div>
                        )}

                        {onScrollToAlerts && (
                            <Button type="button" variant="link" size="sm" onClick={handleCreateAlertForSymbol} className="text-primary h-auto p-0 justify-start text-xs hover:underline disabled:text-muted-foreground/70">
                            <BellPlus className="mr-1 h-3 w-3"/> Criar/Ver alerta para {assetSymbolDisplay}
                            </Button>
                        )}
                    </div>
                )}
            </div>

            <Separator />

            <div className="space-y-4 p-4 border rounded-lg bg-card/60 shadow-sm">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="orderType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tipo de Ordem</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value as "market" | "limit");
                              if (value === "market") {
                                form.setValue("price", undefined, { shouldValidate: true });
                              }
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger disabled={!selectedSymbol || !!symbolInfoError}>
                                <SelectValue placeholder="Selecione o tipo de ordem" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="market">Mercado</SelectItem>
                              <SelectItem value="limit">Limite</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                     <FormField
                        control={form.control}
                        name="side"
                        render={({ field }) => (
                            <FormItem>
                            <FormLabel>Lado</FormLabel>
                            <FormControl>
                                <Tabs
                                defaultValue="buy"
                                onValueChange={(value) => field.onChange(value as "buy" | "sell")}
                                value={field.value}
                                className="w-full pt-0.5"
                                >
                                <TabsList className="grid w-full grid-cols-2" style={{pointerEvents: (!selectedSymbol || !!symbolInfoError) ? 'none' : 'auto', opacity: (!selectedSymbol || !!symbolInfoError) ? 0.7 : 1}}>
                                    <TabsTrigger value="buy" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400 data-[state=active]:border-green-500/30">Comprar/Longo</TabsTrigger>
                                    <TabsTrigger value="sell" className="data-[state=active]:bg-red-500/20 data-[state=active]:text-red-400 data-[state=active]:border-red-500/30">Vender/Curto</TabsTrigger>
                                </TabsList>
                                </Tabs>
                            </FormControl>
                            <FormMessage />
                            </FormItem>
                        )}
                        />
                </div>
                <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel>Preço de Entrada (USDT) para Ordem Limite</FormLabel>
                        <FormControl>
                        <Input
                            type="text"
                            inputMode="decimal"
                            placeholder={currentOrderType === "market" ? "Execução a Mercado" : (displayMarketPrice ? `Ex: ${displayMarketPrice.replace(/[^\d,.]/g, '').replace(',','.')}` : "Preço limite")}
                            disabled={currentOrderType === "market" || !selectedSymbol || !!symbolInfoError}
                            className={currentOrderType === "market" ? "text-muted-foreground italic" : ""}
                            {...field}
                            value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                            onChange={(e) => field.onChange(e.target.value)}
                         />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
            </div>

            <Separator />

            <div className="space-y-4 p-4 border rounded-lg bg-card/60 shadow-sm">
                 <FormField
                    control={form.control}
                    name="orderUsdtAmount"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel className="flex items-center gap-1 text-sm">
                                <DollarSign className="h-4 w-4 text-muted-foreground"/> Valor Nocional da Ordem (USDT)
                            </FormLabel>
                            <FormControl>
                                <Input
                                    type="text"
                                    inputMode="decimal"
                                    placeholder={
                                        isLoadingSymbolInfo && selectedSymbol && !calculatedMinNotional && !numericMinNotional && !symbolInfoError ? "Carregando min. nocional..." :
                                        !isLoadingSymbolInfo && (calculatedMinNotional || numericMinNotional) && !symbolInfoError ? 
                                          (!isUserEditing ? `Auto: ${(calculatedMinNotional ?? numericMinNotional)?.toFixed(2)} USDT` : `Mín: ${(calculatedMinNotional ?? numericMinNotional)?.toFixed(2)} USDT`) :
                                        !isLoadingSymbolInfo && selectedSymbol && (symbolInfoError || (!calculatedMinNotional && !numericMinNotional)) ? "Min. nocional indisponível" :
                                        "Insira o valor em USDT"
                                    }
                                    disabled={!selectedSymbol || !!symbolInfoError || isLoadingSymbolInfo}
                                    {...field}
                                    value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                                    onChange={(e) => {
                                      const processedValue = handleOrderAmountChange(e.target.value);
                                      field.onChange(processedValue);
                                    }}
                                />
                            </FormControl>
                            <FormMessage />
                             <p className="text-xs text-muted-foreground mt-1 break-words">
                               Insira o valor total da sua ordem em USDT. O mínimo nocional para {selectedSymbol || "o par"} é aproximadamente ${(calculatedMinNotional ?? numericMinNotional)?.toFixed(2) || "N/A"}.
                               O sistema usará este valor nocional para a ordem. Se este valor, após a conversão para a quantidade do ativo base, não atender aos filtros da Binance (ex: `minQty`, `stepSize`), a API rejeitará a ordem.
                            </p>
                        </FormItem>
                    )}
                 />


                 <FormField
                    control={form.control}
                    name="leverage"
                    render={({ field }) => {
                      const leverageDisplayValue = typeof field.value === 'number' && isFinite(field.value) ? field.value : 10;
                      return (
                        <FormItem>
                          <FormLabel className="flex justify-between">
                            <span>Alavancagem</span>
                            <span className="text-primary font-semibold">{leverageDisplayValue}x</span>
                          </FormLabel>
                          <FormControl>
                            <Slider
                              min={1}
                              max={125}
                              step={1}
                              value={[leverageDisplayValue]}
                              onValueChange={(value) => field.onChange(value[0])}
                              disabled={!selectedSymbol || !!symbolInfoError || !currentOrderUsdtAmount || currentOrderUsdtAmount <=0}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                <div>
                    <FormLabel className="flex items-center gap-1 text-sm mb-1">
                        <DollarSign className="h-4 w-4 text-muted-foreground"/> Custo Estimado da Margem (USDT)
                    </FormLabel>
                    <div className="h-10 w-full rounded-md border border-input bg-muted/50 px-3 py-2 text-sm text-foreground font-semibold flex items-center">
                        {isLoadingSymbolInfo && selectedSymbol && !symbolInfoError && estimatedMargin === null && currentOrderUsdtAmount !== undefined ? (
                            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Calculando...</>
                        ) : !isLoadingSymbolInfo && selectedSymbol && symbolInfoError && !estimatedMargin ? (
                            <div className="text-destructive flex items-center gap-1 text-xs break-words">
                                <ServerCrash className="h-3.5 w-3.5"/> {symbolInfoError.includes("MinNotional") || symbolInfoError.includes("StepSize") || symbolInfoError.includes("minQty") ? symbolInfoError : "Erro ao obter dados para cálculo."}
                            </div>
                        ) : !isLoadingSymbolInfo && selectedSymbol && !symbolInfoError && estimatedMargin !== null && estimatedMargin > 0 ? (
                            <>${estimatedMargin.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 4})}</>
                        ) : !isLoadingSymbolInfo && selectedSymbol && !symbolInfoError && (estimatedMargin === null || estimatedMargin <= 0) && currentOrderUsdtAmount !== undefined && currentOrderUsdtAmount > 0 ? (
                             <div className="text-muted-foreground text-xs">Não foi possível calcular. Verifique o valor da ordem.</div>
                        ) : !isLoadingSymbolInfo && selectedSymbol && !symbolInfoError && estimatedMargin === null && (currentOrderUsdtAmount === undefined || currentOrderUsdtAmount <=0) ? (
                            <div className="text-muted-foreground text-xs">Insira um valor nocional para a ordem.</div>
                        ) : !selectedSymbol ? (
                            <span className="text-muted-foreground">Selecione um símbolo.</span>
                        ) :  (isLoadingSymbolInfo && !selectedSymbol) ? ( 
                             <span className="text-muted-foreground">Aguardando seleção de símbolo...</span>
                        ) : <span className="text-muted-foreground text-xs">Aguardando dados...</span> }
                    </div>
                     <p className="text-xs text-muted-foreground mt-1 break-words">
                        Este é o valor aproximado que será deduzido da sua carteira para abrir esta posição, com base no valor nocional da ordem e na alavancagem.
                    </p>
                </div>
            </div>

            <div className="space-y-4 p-4 border rounded-lg bg-card/60 shadow-sm">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="takeProfit"
                  render={({ field }) => (
                    <FormItem>
                        <FormLabel>Take Profit (Preço) <span className="text-xs text-muted-foreground">(Ignorado na ordem real)</span></FormLabel>
                        <FormControl>
                            <Input
                                type="text"
                                inputMode="decimal"
                                placeholder="Opcional"
                                disabled={!selectedSymbol || !!symbolInfoError}
                                {...field}
                                value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                                onChange={(e) => field.onChange(e.target.value)}
                            />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}/>
                <FormField
                  control={form.control}
                  name="stopLoss"
                  render={({ field }) => (
                    <FormItem>
                        <FormLabel>Stop Loss (Preço) <span className="text-xs text-muted-foreground">(Ignorado na ordem real)</span></FormLabel>
                        <FormControl>
                            <Input
                                type="text"
                                inputMode="decimal"
                                placeholder="Opcional"
                                disabled={!selectedSymbol || !!symbolInfoError}
                                {...field}
                                value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                                onChange={(e) => field.onChange(e.target.value)}
                            />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}/>
              </div>
               <p className="text-xs text-muted-foreground break-words">Take Profit e Stop Loss devem ser configurados separadamente após a abertura da posição, diretamente na Binance ou através de funcionalidades avançadas de ordens OCO (não implementadas aqui).</p>
            </div>

            <Button
                type="submit"
                className={cn( "w-full font-semibold text-lg py-6", formSide === "buy" ? "bg-green-600 hover:bg-green-700 text-white" : "bg-red-600 hover:bg-red-700 text-white" )}
                disabled={
                  !form.formState.isValid ||
                  form.formState.isSubmitting ||
                  isPlacingOrder ||
                  isLoadingSymbolInfo ||
                  !currentStepSize || (currentStepSize && parseFloat(currentStepSize) <= 0) ||
                  currentMinQty === null || currentMinQty === undefined || (currentMinQty && parseFloat(currentMinQty) < 0) ||
                  (form.getValues("orderType") === 'limit' && (!currentTickSize || (currentTickSize && parseFloat(currentTickSize) <= 0))) ||
                  !form.getValues("orderUsdtAmount") || form.getValues("orderUsdtAmount") <=0 ||
                  ((calculatedMinNotional ?? numericMinNotional) !== null && form.getValues("orderUsdtAmount") !== undefined && form.getValues("orderUsdtAmount")! < (calculatedMinNotional ?? numericMinNotional)!) ||
                  !selectedSymbol ||
                  !clientKeysPresent ||
                  estimatedMargin === null || estimatedMargin <= 0 ||
                  !!symbolInfoError
                }
            >
                {(isLoadingSymbolInfo && selectedSymbol && !symbolInfoError) ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : isPlacingOrder ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : null}
                {(isLoadingSymbolInfo && selectedSymbol && !symbolInfoError) ? "Carregando Infos..." : isPlacingOrder ? "Colocando Ordem..." : `${formSide === "buy" ? "Comprar/Longo" : "Vender/Curto"} ${form.getValues("symbol") ? ` (${assetSymbolDisplay})` : ''}`}
            </Button>
          </form>
        </Form>

        <AlertDialog open={isConfirmingOrder} onOpenChange={(open) => { if (isPlacingOrder) return; setIsConfirmingOrder(open); if (!open) setConfirmedOrderDetails(null); }}>
          <AlertDialogContent className="max-w-xs rounded-lg sm:max-w-sm md:max-w-md lg:max-w-lg overflow-hidden">
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar Ordem Real (via API)</AlertDialogTitle>
              {confirmedOrderDetails && (
                 <AlertDialogDescription asChild>
                    <div className="space-y-1.5 text-sm text-muted-foreground break-words break-all">
                      <div className="font-semibold text-destructive"> ATENÇÃO: Você está prestes a enviar uma ORDEM REAL para a Binance Futures. Verifique todos os detalhes. Esta ação é irreversível e pode resultar em perdas financeiras. </div>
                      <div><strong>Lado:</strong> <span className={cn(confirmedOrderDetails.side === 'buy' ? 'text-green-500' : 'text-red-500')}>{confirmedOrderDetails.side === 'buy' ? 'COMPRAR/LONGO' : 'VENDER/CURTO'}</span></div>
                      <div><strong>Símbolo:</strong> {confirmedOrderDetails.symbol}</div>
                      <div><strong>Tipo:</strong> {confirmedOrderDetails.orderType === 'market' ? 'Mercado' : 'Limite'}</div>
                      <div><strong>Valor Nocional da Ordem (USDT):</strong> ${confirmedOrderDetails.quantity.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                      <div><strong>Alavancagem:</strong> {confirmedOrderDetails.leverage}x</div>
                      <div><strong>Custo Estimado da Margem (USDT):</strong> ${confirmedOrderDetails.estimatedMargin.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 4})}</div>
                      {confirmedOrderDetails.orderType === 'limit' && confirmedOrderDetails.price !== undefined && <div><strong>Preço Limite:</strong> ${confirmedOrderDetails.price.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 8})}</div>}

                      <div className="pt-2 text-xs text-muted-foreground/80">
                          <strong>Execução (Ordens a Mercado):</strong> A ordem será executada ao melhor preço de mercado disponível. O preço de mercado pode variar (slippage). O sistema usa o valor nocional informado e o preço de referência atual para calcular a quantidade do ativo base. A aceitação final pela Binance depende dos filtros do par (ex: quantidade mínima do ativo, precisão).
                      </div>
                       <div className="mt-1 text-xs text-muted-foreground/80">
                          <strong>Execução (Ordens Limite):</strong> A ordem será colocada ao preço limite especificado ou melhor. O cálculo da quantidade do ativo base também usa o valor nocional informado. A aceitação final pela Binance depende dos filtros do par. Se o preço de mercado não atingir seu limite, a ordem pode não ser executada.
                      </div>
                      <div className="mt-1 text-xs text-muted-foreground/80">Take Profit e Stop Loss (se informados no formulário) são ignorados para esta colocação de ordem principal e devem ser configurados separadamente na Binance.</div>
                      <div className="font-bold text-destructive mt-2 text-xs">Prossiga apenas se tiver certeza e entender os riscos. Teste exaustivamente no Testnet da Binance antes de usar fundos reais.</div>
                    </div>
                </AlertDialogDescription>
              )}
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isPlacingOrder}>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmOrder}
                disabled={isPlacingOrder}
                className={cn(
                    buttonVariants({
                        className: confirmedOrderDetails?.side === "buy"
                                    ? "bg-green-600 hover:bg-green-700 text-white"
                                    : "bg-red-600 hover:bg-red-700 text-white"
                    })
                )}
              >
                {isPlacingOrder ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                {isPlacingOrder ? "Colocando Ordem..." : "Confirmar e Colocar Ordem"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <Card className="mt-6 bg-card/40 border-dashed border-border/70">
            <CardHeader className="pb-2"> <CardTitle className="flex items-center gap-2 text-base"><Info className="text-blue-500 h-5 w-5"/> Sobre este Terminal</CardTitle> </CardHeader>
            <CardContent className="text-xs text-muted-foreground space-y-1.5">
                <div className="break-words"><strong>Operações Reais (via API):</strong> Este terminal tentará enviar ordens reais para a Binance Futures se as chaves de API corretas estiverem configuradas na página de Configurações e fornecidas para esta operação.</div>
                <div className="break-words"><strong>Valor Nocional da Ordem (USDT):</strong> Você define o valor total da sua ordem em USDT. O sistema usa este valor para calcular a quantidade do ativo base. O valor mínimo nocional (`minNotional`) para o par é exibido como referência. Sua ordem deve atender a este mínimo e também aos filtros de quantidade mínima (`minQty`) e precisão (`stepSize`) do ativo base.</div>
                <div className="break-words"><strong>Custo Estimado da Margem (USDT):</strong> Mostra o valor aproximado que será deduzido da sua carteira para abrir esta posição, com base no valor nocional que você inseriu e na alavancagem selecionada.</div>
                <div className="break-words"><strong>Precisão de Preço (Ordens Limite):</strong> Para ordens limite, o preço é ajustado para a precisão correta definida pelo `tickSize` do par antes de ser enviado à Binance.</div>
                <div className="break-words"><strong>Aceitação da Ordem:</strong> A API da Binance fará a validação final. Se o Valor Nocional ou a quantidade do ativo base calculada não atenderem aos filtros do par (ex: `minQty`, `stepSize`), ou o preço (para ordens limite) não atender ao `tickSize`, a ordem será rejeitada e o erro específico será exibido.</div>
                <div className="break-words"><strong>Take Profit / Stop Loss:</strong> Os campos de TP/SL no formulário são atualmente ignorados para a colocação da ordem principal e devem ser gerenciados separadamente na Binance.</div>
                <div className="font-bold text-destructive break-words">USE COM EXTREMA CAUTELA. Teste exaustivamente no Testnet da Binance antes de usar com fundos reais.</div>
            </CardContent>
        </Card>
      </CardContent>
    </Card>
  );
}

